package com.siact.energy.cal.server.common.datasource.db.factory;

import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.enums.DBATypeEnum;
import com.siact.energy.cal.server.common.datasource.db.core.AbstractDbOperator;
import com.siact.energy.cal.server.common.datasource.db.core.AbstractSqlBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Component
public class TimeSeriesDbFactory {
    private final Map<Integer, AbstractSqlBuilder> sqlBuilderMap;
    private final Map<Integer, AbstractDbOperator> dbOperatorMap;
    
    @Autowired
    public TimeSeriesDbFactory(List<AbstractSqlBuilder> sqlBuilders,
                               List<AbstractDbOperator> dbOperators) {
        // 初始化SQL生成器映射
        sqlBuilderMap = new HashMap<>();
        sqlBuilders.forEach(builder -> {
            String type = builder.getClass().getAnnotation(Service.class).value();
            if ("tdengineSqlBuilder".equals(type)) {
                sqlBuilderMap.put(DBATypeEnum.TDENGINE.getIndex(), builder);
            } else if ("influxDBSqlBuilder".equals(type)) {
                sqlBuilderMap.put(DBATypeEnum.INFLUXDB.getIndex(), builder);
            } else if ("IotDBSqlBuilder".equals( type)){
                sqlBuilderMap.put(DBATypeEnum.IOTDB.getIndex(), builder);
            }
        });
        
        // 初始化数据库操作器映射
        dbOperatorMap = new HashMap<>();
        dbOperators.forEach(operator -> {
            String type = operator.getClass().getAnnotation(Service.class).value();
            if ("tdengineOperator".equals(type)) {
                dbOperatorMap.put(DBATypeEnum.TDENGINE.getIndex(), operator);
            } else if ("influxDBOperator".equals(type)) {
                dbOperatorMap.put(DBATypeEnum.INFLUXDB.getIndex(), operator);
            } else if ("IotDBOperator".equals(type)){
                dbOperatorMap.put(DBATypeEnum.IOTDB.getIndex(), operator);
            }
        });
    }
    
    public AbstractSqlBuilder getSqlBuilder(Integer dbType) {
        return Optional.ofNullable(sqlBuilderMap.get(dbType))
            .orElseThrow(() -> new BizException("不支持的数据源类型: " + dbType));
    }
    
    public AbstractDbOperator getDbOperator(Integer dbType) {
        return Optional.ofNullable(dbOperatorMap.get(dbType))
            .orElseThrow(() -> new BizException("不支持的数据源类型: " + dbType));
    }
}