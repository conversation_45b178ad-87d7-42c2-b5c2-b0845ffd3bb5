package com.siact.energy.cal.server.common.datasource.db.impl.iotdb;

import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.dto.energycal.TimeQueryDTO;
import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;
import com.siact.energy.cal.server.common.datasource.db.core.AbstractSqlBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Classname IotDBSqlBuilder
 * @Description TODO
 * @Date 2025/7/17 17:16
 * @Created by wang kai
 */
@Service("IotDBSqlBuilder")
@Slf4j
public class IotDBSqlBuilder extends AbstractSqlBuilder {
    @Override
    public String buildBasicQuerySql(TimeQueryDTO queryDTO, DataSourceVo dataSourceVo) {
        List<String> devpropertyList = queryDTO.getDataCodes();
        String devpropListStr = devpropertyList.stream()
                .map(value -> "'" + value + "'") // 将每个元素用单引号括起来
                .collect(Collectors.joining(","));
        String sql = "";
        if (!queryDTO.isEquallySpacedQuery()) {
            sql = String.format("SELECT devproperty,first(itemvalue) as itemvalue, first_by(time,itemvalue) as ts  FROM %s WHERE devproperty in (%s) and time >= %s AND time <= %s group by devproperty;" +
                            "SELECT devproperty,last(itemvalue) as itemvalue,last_by(time,itemvalue) as ts FROM %s WHERE devproperty in (%s) and time >= %s AND time <= %s group by devproperty;",
                    dataSourceVo.getTableName(),
                    devpropListStr,
                    queryDTO.getStartTime(),
                    queryDTO.getEndTime(),
                    dataSourceVo.getTableName(),
                    devpropListStr,
                    queryDTO.getStartTime(),
                    queryDTO.getEndTime());
        } else {
            // 拼凑步长
            String intervalStr = queryDTO.getInterval() + queryDTO.getTsUnit();
            if (intervalStr.contains("M")){
                intervalStr = intervalStr.replace("M", "mo");
            }
            sql = String.format("SELECT  date_bin(%s,time) as ts,first(itemvalue) as itemvalue,devproperty FROM %s WHERE devproperty in (%s) and time >= %s AND time <= %s group by 1,devproperty;",
                    intervalStr,
                    dataSourceVo.getTableName(),
                    devpropListStr,
                    queryDTO.getStartTime(),
                    queryDTO.getEndTime()
            );
        }
        return sql;
    }

    @Override
    public String buildTimeRangeAggSql(String function, Map<String, String> propMapping, TimeQueryDTO queryDTO, String tableName) {
        // 将设备属性列表转换为SQL IN子句格式
        String devpropListStr = propMapping.keySet().stream()
                .map(value -> "'" + value + "'")
                .collect(Collectors.joining(","));

        // SQL模板
        String sqlTemplate = "SELECT first(time) as ts,devproperty,%s(itemvalue) as itemvalue " +
                "FROM %s " +
                "WHERE devproperty in (%s) " +
                "and time >= %s " +
                "AND time <= %s " +
                "group by devproperty";

        Set<String> supportedFunctions = new HashSet<>(Arrays.asList("sum", "avg", "max", "min", "last", "first"));
        if (!supportedFunctions.contains(function)) {
            throw new BizException("不支持的聚合函数: " + function);
        }

        return String.format(sqlTemplate,
                function,
                tableName,
                devpropListStr,
                queryDTO.getStartTime(),
                queryDTO.getEndTime());
    }

    @Override
    public String buildIntervalAggSql(String function, Map<String, String> propMapping, TimeQueryDTO queryDTO, String tableName) {
        // 将设备属性列表转换为SQL IN子句格式
        String devpropListStr = propMapping.keySet().stream()
                .map(value -> "'" + value + "'")
                .collect(Collectors.joining(","));

        // 构建时间间隔
        String intervalUnit = queryDTO.getInterval() + queryDTO.getTsUnit();

        if (intervalUnit.contains("M")){
            intervalUnit = intervalUnit.replace("M", " mo");
        }
        // SQL模板
        String sqlTemplate = "SELECT date_bin(%s,time) as ts,devproperty,%s(itemvalue) as itemvalue  " +
                "FROM  %s " +
                "WHERE devproperty in (%s) " +
                "and time >= %s " +
                "AND time <= %s " +
                "group by 1,devproperty";

        Set<String> supportedFunctions = new HashSet<>(Arrays.asList("sum", "avg", "max", "min", "last", "first"));
        if (!supportedFunctions.contains(function)) {
            throw new BizException("不支持的聚合函数: " + function);
        }

        return String.format(sqlTemplate,
                intervalUnit,
                function,
                tableName,
                devpropListStr,
                queryDTO.getStartTime(),
                queryDTO.getEndTime()
                );
    }

    @Override
    public String buildTimeSliceDataSql(String startTime, String endTime, List<String> dataCOdes, String tableName) {
        String devpropListStr = dataCOdes.stream()
                .map(value -> "'" + value + "'") // 将每个元素用单引号括起来
                .collect(Collectors.joining(","));

        String sql = String.format("SELECT last(time) as ts, last(itemvalue) as itemvalue,devproperty FROM %s WHERE devproperty in (%s) and time >=  %s AND time <= %s group by devproperty;",
                tableName,
                devpropListStr,
                startTime,
                endTime);
        return sql;
    }

    @Override
    public String buildDiffSql(Map<String, String> propMapping, String tableName, String startTime, String endTime, String interval, String unit) {
        return null;
    }
}
