package com.siact.energy.cal.server.common.config;

import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.ibatis.logging.LogFactory;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

/**
 * 数据库优化配置类
 * 用于解决频繁的数据库连接和日志打印问题
 * 
 * <AUTHOR>
 * @create 2024/8/7
 */
@Configuration
@EnableTransactionManagement
public class DatabaseOptimizationConfig {

    @Autowired
    private DataSource dataSource;

    /**
     * 配置事务管理器
     */
    @Bean
    @Primary
    public PlatformTransactionManager transactionManager() {
        DataSourceTransactionManager transactionManager = new DataSourceTransactionManager();
        transactionManager.setDataSource(dataSource);
        // 设置事务超时时间（秒）
        transactionManager.setDefaultTimeout(300);
        return transactionManager;
    }

    /**
     * 优化 MyBatis SqlSessionFactory 配置
     */
    @Bean("optimizedSqlSessionFactory")
    public SqlSessionFactory sqlSessionFactory() throws Exception {
        // 禁用 MyBatis 的详细日志输出
        LogFactory.useNoLogging();
        
        MybatisSqlSessionFactoryBean factory = new MybatisSqlSessionFactoryBean();
        factory.setDataSource(dataSource);
        
        // 配置 GlobalConfig
        GlobalConfig globalConfig = new GlobalConfig();
        // 禁用 banner
        globalConfig.setBanner(false);
        
        // 设置全局配置
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        
        // 优化配置项
        configuration.setMapUnderscoreToCamelCase(true);
        configuration.setCacheEnabled(true);
        configuration.setLazyLoadingEnabled(false);
        configuration.setAggressiveLazyLoading(false);
        configuration.setMultipleResultSetsEnabled(true);
        configuration.setUseColumnLabel(true);
        configuration.setUseGeneratedKeys(false);
        configuration.setAutoMappingBehavior(org.apache.ibatis.session.AutoMappingBehavior.PARTIAL);
        configuration.setAutoMappingUnknownColumnBehavior(org.apache.ibatis.session.AutoMappingUnknownColumnBehavior.WARNING);
        configuration.setDefaultExecutorType(org.apache.ibatis.session.ExecutorType.REUSE);
        configuration.setDefaultStatementTimeout(25);
        configuration.setDefaultFetchSize(100);
        configuration.setSafeRowBoundsEnabled(false);
        configuration.setLocalCacheScope(org.apache.ibatis.session.LocalCacheScope.SESSION);
        configuration.setJdbcTypeForNull(org.apache.ibatis.type.JdbcType.OTHER);
        configuration.setCallSettersOnNulls(false);
        configuration.setReturnInstanceForEmptyRow(false);
        
        // 关闭日志实现
        configuration.setLogImpl(org.apache.ibatis.logging.nologging.NoLoggingImpl.class);
        
        factory.setConfiguration(configuration);
        
        return factory.getObject();
    }
    
    /**
     * 静态初始化块，在类加载时就禁用 MyBatis 日志
     */
    static {
        // 禁用 MyBatis 的所有日志输出
        LogFactory.useNoLogging();
        
        // 设置系统属性来禁用详细的数据库日志
        System.setProperty("mybatis.configuration.log-impl", "org.apache.ibatis.logging.nologging.NoLoggingImpl");
        
        // 禁用 HikariCP 的详细日志
        System.setProperty("com.zaxxer.hikari.housekeeping.periodMs", "30000");
    }
}
