package com.siact.energy.cal.server.common.config;

import org.apache.ibatis.logging.LogFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 应用启动配置类
 * 在应用启动时执行一些优化配置
 * 
 * <AUTHOR>
 * @create 2024/8/7
 */
@Component
@Order(1)
public class ApplicationStartupConfig implements ApplicationRunner {

    private static final Logger logger = LoggerFactory.getLogger(ApplicationStartupConfig.class);

    @Override
    public void run(ApplicationArguments args) throws Exception {
        logger.info("开始执行应用启动优化配置...");
        
        // 禁用 MyBatis 详细日志
        disableMyBatisVerboseLogging();
        
        // 设置数据库连接优化参数
        setDatabaseOptimizations();
        
        // 设置 JVM 优化参数
        setJvmOptimizations();
        
        logger.info("应用启动优化配置完成");
    }
    
    /**
     * 禁用 MyBatis 详细日志输出
     */
    private void disableMyBatisVerboseLogging() {
        try {
            // 禁用 MyBatis 的所有日志输出
            LogFactory.useNoLogging();
            
            // 设置系统属性
            System.setProperty("mybatis.configuration.log-impl", "org.apache.ibatis.logging.nologging.NoLoggingImpl");
            System.setProperty("mybatis-plus.configuration.log-impl", "org.apache.ibatis.logging.nologging.NoLoggingImpl");
            
            logger.info("已禁用 MyBatis 详细日志输出");
        } catch (Exception e) {
            logger.warn("禁用 MyBatis 日志时出现异常: {}", e.getMessage());
        }
    }
    
    /**
     * 设置数据库连接优化参数
     */
    private void setDatabaseOptimizations() {
        try {
            // HikariCP 优化参数
            System.setProperty("com.zaxxer.hikari.housekeeping.periodMs", "30000");
            System.setProperty("com.zaxxer.hikari.aliveBypassWindowMs", "500");
            
            // MySQL 连接器优化参数
            System.setProperty("com.mysql.cj.disableAbandonedConnectionCleanup", "true");
            
            logger.info("已设置数据库连接优化参数");
        } catch (Exception e) {
            logger.warn("设置数据库优化参数时出现异常: {}", e.getMessage());
        }
    }
    
    /**
     * 设置 JVM 优化参数
     */
    private void setJvmOptimizations() {
        try {
            // 设置日志相关的系统属性
            System.setProperty("org.apache.commons.logging.Log", "org.apache.commons.logging.impl.NoOpLog");
            System.setProperty("java.util.logging.config.file", "");
            
            // 禁用一些不必要的日志
            System.setProperty("spring.jpa.show-sql", "false");
            System.setProperty("spring.jpa.properties.hibernate.format_sql", "false");
            System.setProperty("spring.jpa.properties.hibernate.use_sql_comments", "false");
            
            logger.info("已设置 JVM 优化参数");
        } catch (Exception e) {
            logger.warn("设置 JVM 优化参数时出现异常: {}", e.getMessage());
        }
    }
}
