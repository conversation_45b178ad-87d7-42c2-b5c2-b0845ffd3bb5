package com.siact.energy.cal.server.common.datasource.db.impl.iotdb;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;
import com.siact.energy.cal.common.util.utils.DateUtils;
import com.siact.energy.cal.server.common.datasource.db.core.AbstractDbOperator;
import com.siact.energy.cal.server.entity.energycal.DiffCal;
import com.singularsys.jep.functions.Str;
import lombok.extern.slf4j.Slf4j;
import org.apache.iotdb.isession.ITableSession;
import org.apache.iotdb.isession.SessionDataSet;
import org.apache.iotdb.rpc.IoTDBConnectionException;
import org.apache.iotdb.session.TableSessionBuilder;
import org.apache.tsfile.enums.TSDataType;
import org.apache.tsfile.read.common.RowRecord;
import org.apache.tsfile.write.record.Tablet;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.sql.Connection;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @Classname IotDBOperator
 * @Description TODO   兼容union语法，需要对sql做数组判断
 * @Date 2025/7/17 17:16
 * @Created by wang kai
 */
@Service("IotDBOperator")
@Slf4j
public class IotDBOperator extends AbstractDbOperator {
    @Override
    public Connection getConnection(DataSourceVo dataSourceVo) {
        return null;
    }

    @Override
    public void executeBasicSql(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, String sql, DataSourceVo dataSourceVo) {
        log.info(" ===============>    IOT DB 执行SQL：{}",sql);
        String[] split = sql.split(";");
        for (String sqlItem : split){
            try(ITableSession session = getSession(dataSourceVo);
                SessionDataSet dataSet = session.executeQueryStatement(sqlItem)
            ){

                List<String> columns = dataSet.getColumnNames();
                while (dataSet.hasNext()) {
                    RowRecord row = dataSet.next();
                    Map<String, Object> rowMap = new HashMap<>();
                    for (int i = 0; i < columns.size(); i++) {
                        String col = columns.get(i);
                        rowMap.put(col, row.getFields().get(i).getObjectValue(row.getFields().get(i).getDataType()));
                    }

                    String ts = DateUtils.longToStr(Long.parseLong(rowMap.get("ts").toString()));
                    String property = rowMap.get("devproperty").toString();
                    String itemvalue = rowMap.get("itemvalue").toString();
                    if (StrUtil.isNotBlank(itemvalue)){
                        // 将itemvalue转换为Double类型
                        BigDecimal value = null;
                        try {

                            value = new BigDecimal(itemvalue);

                        } catch (NumberFormatException e) {
                            log.error("转换itemvalue异常", e.getMessage());
//                    throw new RuntimeException(e);
                        }
                        resultMap.computeIfAbsent(property, k -> new ConcurrentHashMap<>())
                                .put(ts, value);
                    }

                }

            }catch (Exception e){
                log.error("IOT DB 查询SQL执行失败：{}",e.getMessage(),e);
                throw new BizException("IOT DB 查询SQL执行失败");
            }
        }

    }

    @Override
    public void executeAggQuery(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, String sql, DataSourceVo dataSourceVo, Map<String, String> propMapping) {
        log.info(" ===============>    IOT DB 执行SQL：{}",sql);
        try(ITableSession session = getSession(dataSourceVo);
            SessionDataSet dataSet = session.executeQueryStatement(sql)
        ){List<String> columns = dataSet.getColumnNames();
            while (dataSet.hasNext()) {
                RowRecord row = dataSet.next();
                Map<String, Object> rowMap = new HashMap<>();
                for (int i = 0; i < columns.size(); i++) {
                    String col = columns.get(i);
                    rowMap.put(col, row.getFields().get(i).getObjectValue(row.getFields().get(i).getDataType()));
                }

                String ts = DateUtils.longToStr(Long.parseLong(rowMap.get("ts").toString()));
                String devproperty = rowMap.get("devproperty").toString();
                String itemvalue = rowMap.get("itemvalue").toString();
                String targetProp = propMapping.getOrDefault(devproperty, null);

                if (StrUtil.isNotBlank(itemvalue) && StrUtil.isNotBlank(targetProp)) {
                    BigDecimal value = new BigDecimal(itemvalue);
                    resultMap.computeIfAbsent(targetProp, k -> new ConcurrentHashMap<>())
                            .put(ts, value);
                }
            }
        }catch (Exception e){
            log.error("IOT DB 查询SQL执行失败：{}",e.getMessage(),e);
            throw new BizException("IOT DB 查询SQL执行失败");
        }
    }

    @Override
    public void executeDiffQuery(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, String sql, DataSourceVo dataSourceVo, Map<String, String> propMapping) {
        log.info(" ===============>    IOT DB 执行SQL：{}",sql);
        String[] split = sql.split(";");
        for (String sqlItem : split){
            try(ITableSession session = getSession(dataSourceVo);
                SessionDataSet dataSet = session.executeQueryStatement( sqlItem)){
                List<String> columns = dataSet.getColumnNames();
                List<DiffCal> list = new ArrayList<>();
                while (dataSet.hasNext()) {
                    RowRecord row = dataSet.next();
                    Map<String, Object> rowMap = new HashMap<>();
                    for (int i = 0; i < columns.size(); i++) {
                        String col = columns.get(i);
                        rowMap.put(col, row.getFields().get(i).getObjectValue(row.getFields().get(i).getDataType()));
                    }
                    String ts = DateUtils.longToStr(Long.parseLong(rowMap.get("ts").toString()));
                    String devproperty = rowMap.get("devproperty").toString();
                    String itemvalue = rowMap.get("itemvalue").toString();
                    String targetProp = propMapping.getOrDefault(devproperty, null);
                    if (StrUtil.isNotBlank(targetProp)){
                        DiffCal diffCal = new DiffCal();
                        diffCal.setTs( ts);
                        diffCal.setTargetProp(targetProp);
                        diffCal.setItemvalue(itemvalue);
                        list.add(diffCal);
                    }
                }

                //排序
                list.sort(Comparator.comparing(DiffCal::getTs));
                //分组并计算差值
                calculateDiffByDevproperty(resultMap,list);
            }catch (Exception e){
                log.error("IOT DB 获取数据失败：{}",e.getMessage(),e);
                throw new BizException("IOTDB Query execution failed");
            }
        }

    }

    @Override
    public void insertData(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                           List<String> dataCodes,
                           DataSourceVo dataSourceVo) {
        List<String> columns = Arrays.asList("itemvalue", "currentts", "modelvalue", "initialvalue", "qt", "gateway", "devcode", "devproperty", "itemid", "tenantid",
                "projectid");
        List<TSDataType> types = Arrays.asList(
                TSDataType.DOUBLE,
                TSDataType.TIMESTAMP,
                TSDataType.DOUBLE,
                TSDataType.STRING,
                TSDataType.INT32,
                TSDataType.STRING,
                TSDataType.STRING,
                TSDataType.STRING,
                TSDataType.STRING,
                TSDataType.STRING,
                TSDataType.STRING
        );
        List<Tablet.ColumnCategory> columnCategories = Arrays.asList(
                Tablet.ColumnCategory.FIELD,
                Tablet.ColumnCategory.FIELD,
                Tablet.ColumnCategory.FIELD,
                Tablet.ColumnCategory.FIELD,
                Tablet.ColumnCategory.FIELD,
                Tablet.ColumnCategory.TAG,
                Tablet.ColumnCategory.TAG,
                Tablet.ColumnCategory.TAG,
                Tablet.ColumnCategory.TAG,
                Tablet.ColumnCategory.TAG,
                Tablet.ColumnCategory.TAG
        );

        Tablet tablet = new Tablet(dataSourceVo.getTableName(), columns, types, columnCategories, 500);
        for (String dataCode : dataCodes) {
            Map<String, BigDecimal> tsValueMap = resultMap.getOrDefault(dataCode, null);
            if (!ObjectUtils.isEmpty(tsValueMap)){
                for (Map.Entry<String, BigDecimal> entry : tsValueMap.entrySet()) {
                    String ts = entry.getKey();
                    long time = convertToTimestamp(ts);
                    BigDecimal value = entry.getValue();
                    int index = tablet.getRowSize();
                    tablet.addTimestamp(index,time);
                    tablet.addValue("devproperty",index,dataCode);

                    tablet.addValue("itemvalue",index,((BigDecimal) value).doubleValue());

                    if (tablet.getRowSize()==tablet.getMaxRowNumber()){
                        try(ITableSession session = getSession(dataSourceVo);){
                            session.insert(tablet);
                            tablet.reset();
                        }catch (Exception e){
                            log.error("IOT DB 插入数据失败：{}",e.getMessage(),e);
                        }
                    }
                }
            }
        }
        if (tablet.getRowSize()>0){
            try(ITableSession session = getSession(dataSourceVo);){
                session.insert(tablet);
                tablet.reset();
            }catch (Exception e){
                log.error("IOT DB 插入数据失败：{}",e.getMessage(),e);
            }
        }

    }

    public static long convertToTimestamp(String dateTimeStr) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date date = dateFormat.parse(dateTimeStr);
            return date.getTime();
        } catch (ParseException e) {
            e.printStackTrace();
            // 根据需求处理异常
            return -1;
        }
    }

    @Override
    public boolean testConnection(DataSourceVo testDTO) {
        try(ITableSession session = getSession(testDTO);){
            SessionDataSet dataSet = session.executeQueryStatement("SHOW VERSION");
            return dataSet != null && dataSet.hasNext();
        }catch (Exception e){
            log.error("IOT DB 测试连接失败：{}",e.getMessage(),e);
        }
        return false;
    }



    public ITableSession getSession(DataSourceVo dataSourceVo) throws IoTDBConnectionException {
        return new TableSessionBuilder()
                .nodeUrls(Collections.singletonList(dataSourceVo.getDatabaseIp()+":"+dataSourceVo.getDatabasePort()))
                .username(dataSourceVo.getUserName())
                .password(dataSourceVo.getPassword())
                .database(dataSourceVo.getDb())
                .build();
    }


    private void calculateDiffByDevproperty(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, List<DiffCal> list) {
        Map<String, List<DiffCal>> groupedData = list.stream()
                .collect(Collectors.groupingBy(DiffCal::getTargetProp));

        for (Map.Entry<String, List<DiffCal>> entry : groupedData.entrySet()) {
            List<DiffCal> dataPoints = entry.getValue();
            for (int i = 0; i < dataPoints.size() - 1; i++) {
                DiffCal current = dataPoints.get(i);
                DiffCal next = dataPoints.get(i + 1);
                BigDecimal diff = null;
                if (StrUtil.isNotBlank(current.getItemvalue()) && StrUtil.isNotBlank(next.getItemvalue())) {
                    BigDecimal nextValue = new BigDecimal(next.getItemvalue());
                    BigDecimal currValue = new BigDecimal(current.getItemvalue());

                    diff = nextValue.subtract(currValue);
                }

                if (ObjectUtil.isNotEmpty(diff)) {
                    resultMap.computeIfAbsent(current.getTargetProp(), k -> new ConcurrentHashMap<>())
                            .put(current.getTs(), diff);
                }

            }

        }
    }
}
