package com.siact.energy.cal.server.service.dataProject.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siact.api.common.api.vo.project.TMProjectInsSimpleVo;
import com.siact.api.feign.api.ins.InsService;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.dataProject.DataProjectInsertDTO;
import com.siact.energy.cal.common.pojo.dto.dataProject.DataProjectQueryDTO;
import com.siact.energy.cal.common.pojo.dto.dataProject.DataProjectUpdateDTO;
import com.siact.energy.cal.common.pojo.enums.DeletedEnum;
import com.siact.energy.cal.common.pojo.vo.dataProject.DataProjectVO;
import com.siact.energy.cal.common.util.utils.ClassUtil;
import com.siact.energy.cal.server.common.utils.SiactSecApiFeignUtil;
import com.siact.energy.cal.server.convertor.dataProject.DataProjectConvertor;
import com.siact.energy.cal.server.dao.dataProject.DataProjectDao;
import com.siact.energy.cal.server.entity.dataProject.DataProject;
import com.siact.energy.cal.server.service.BaseService;
import com.siact.energy.cal.server.service.dataProject.DataProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目表(DataProject)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-14 15:27:00
 */
@Service("dataProjectService")
public class DataProjectServiceImpl extends ServiceImpl<DataProjectDao, DataProject> implements DataProjectService {

    @Autowired
    private BaseService baseService;

    @Autowired
    private InsService insService;

    @Autowired
    private DataProjectDao dataProjectDao;

    @Override
    public PageBean<DataProjectVO> listPage(PageBean<DataProjectVO> page, DataProjectQueryDTO dataProjectQueryDTO) {
        PageBean<DataProjectVO> result = page(page, dataProjectQueryDTO);
        
        // 添加权限过滤
        if (result != null && result.getRecords() != null) {
            List<DataProjectVO> filteredRecords = baseService.filterAuthorizedProjects(
                result.getRecords(),
                project -> project.getProjectCode() // 或其他能标识项目的字段
            );
            result.setRecords(filteredRecords);
            result.setTotal(filteredRecords.size());
        }
        
        return result;
    }

    @Override
    public DataProjectVO getVoById(Serializable id) {
        DataProject dataProject = getById(id);
        return DataProjectConvertor.INSTANCE.entity2Vo(dataProject);
    }

    @Override
    public List<DataProjectVO> listAll() {
        List<DataProjectVO> allProjects = DataProjectConvertor.INSTANCE.entities2Vos(list());
        return baseService.filterAuthorizedProjects(
            allProjects,
            DataProjectVO::getProjectCode  // 直接使用方法引用获取projectCode
        );
    }


    public List<DataProjectVO> getProjectByDigitalTwin() {
        List<TMProjectInsSimpleVo> insSimpleVos = SiactSecApiFeignUtil.list(insService.list(null));
        return DataProjectConvertor.INSTANCE.digitalTwinProjects2VOs(insSimpleVos);
    }

    /**
     * 分页查询
     */
    private PageBean<DataProjectVO> page(PageBean<DataProjectVO> page, DataProjectQueryDTO dataProjectQueryDTO) {
        // 转换器
        DataProjectConvertor convertor = DataProjectConvertor.INSTANCE;
        
        // VO转实体
        DataProject dataProject = convertor.queryDTO2Entity(dataProjectQueryDTO);

        // 创建查询对象
        LambdaQueryWrapper<DataProject> queryWrapper = new LambdaQueryWrapper<>(dataProject);

        // 添加权限过滤条件
        if (baseService.isEnabled()) {
            List<String> authorizedProjectCodes = baseService.getUserProjects().stream()
                    .map(project -> project.getDataCode())
                    .collect(Collectors.toList());
            if (!authorizedProjectCodes.isEmpty()) {
                queryWrapper.in(DataProject::getProjectCode, authorizedProjectCodes);
            } else {
                // 如果没有权限，返回空结果
                return new PageBean<>();
            }
        }

        List<String> voFieldNameList = ClassUtil.getClassAllFields(DataProjectVO.class);
        queryWrapper.select(c -> voFieldNameList.contains(c.getProperty()));

        // 查询实体数据
        Page<DataProject> entityPage = page(convertor.voPageBean2EntityPage(page), queryWrapper);

        // 实体分页转VO分页
        return convertor.entityPage2VoPageBean(entityPage);
    }



    @Override
    public Boolean save(DataProjectInsertDTO dataProjectInsertDTO) {
        return save(DataProjectConvertor.INSTANCE.insertDTO2Entity(dataProjectInsertDTO));
    }


    @Override
    public Boolean updateVoById(DataProjectUpdateDTO dataProjectUpdateDTO) {
        return updateById(DataProjectConvertor.INSTANCE.updateDTO2Entity(dataProjectUpdateDTO));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateProjectByDigitalTwin() {
        return saveOrUpdate(getProjectByDigitalTwin());
    }

    /**
     * 从数字孪生获取项目信息
     *
     * @return 项目信息
     */

    private int saveOrUpdate(List<DataProjectVO> dataProjectVOList) {
        List<DataProject> dataProjectList = DataProjectConvertor.INSTANCE.vos2Entities(dataProjectVOList);

        // 获取现有项目编码集合(包括已删除的)
        Set<String> existingCodes = dataProjectDao.selectListWithDeleted().stream()
                .map(DataProject::getProjectCode)
                .collect(Collectors.toSet());

        Set<String> incomingCodes = new HashSet<>();
        Date now = new Date();

        // 准备插入/更新数据 - 基于project_code的UPSERT操作
        List<DataProject> toUpsert = new ArrayList<>();
        dataProjectList.forEach(vo -> {
            incomingCodes.add(vo.getProjectCode());

            // 统一处理：让数据库基于project_code自动判断插入或更新
            vo.setCreateTime(now);  // 插入时使用，更新时会被忽略
            vo.setUpdateTime(now);  // 插入和更新时都会使用
            vo.setDeleted(DeletedEnum.NORMAL.getValue());
            toUpsert.add(vo);
        });

        // 处理需要标记删除的记录
        List<DataProject> toDelete = existingCodes.stream()
                .filter(code -> !incomingCodes.contains(code))
                .map(code -> {
                    DataProject entity = dataProjectDao.selectByProjectCode(code);
                    entity.setDeleted(DeletedEnum.DELETED.getValue());
                    entity.setUpdateTime(now);
                    return entity;
                })
                .collect(Collectors.toList());

        // 批量执行操作
        int count = 0;

        // 分批处理UPSERT操作，避免锁表
        if (!toUpsert.isEmpty()) {
            count += batchInsertOrUpdate(toUpsert);
        }

        if (!toDelete.isEmpty()) {
            // 过滤掉id为10000的记录，10000为能源数仓，不用删除
            count += dataProjectDao.deleteBatchIds(
                toDelete.stream()
                    .map(DataProject::getId)
                    .filter(id -> id != 10000)
                    .collect(Collectors.toList())
            );
        }

        return count;
    }

    /**
     * 分批处理插入或更新操作，避免锁表
     */
    private int batchInsertOrUpdate(List<DataProject> dataProjects) {
        final int BATCH_SIZE = 10;  // 小批量处理，避免锁表
        int totalCount = 0;

        for (int i = 0; i < dataProjects.size(); i += BATCH_SIZE) {
            int end = Math.min(i + BATCH_SIZE, dataProjects.size());
            List<DataProject> batch = dataProjects.subList(i, end);
            totalCount += dataProjectDao.insertOrUpdateBatch(batch);
        }

        return totalCount;
    }
}

