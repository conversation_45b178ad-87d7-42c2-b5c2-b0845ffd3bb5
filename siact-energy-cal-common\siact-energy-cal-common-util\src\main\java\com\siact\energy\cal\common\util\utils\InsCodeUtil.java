package com.siact.energy.cal.common.util.utils;


import com.siact.code.enums.PropClassify;

/**
 * @Package com.siact.datacal.utils
 * @description:
 * <AUTHOR>
 * @create 2024/5/27 16:14
 */


public class InsCodeUtil {
    final static String defaultDataCode = "P0000000_S0000000_ST00000000_U00000000_EQ000000000000_MP0000000";
    final static String Regex_P = "P0000000";
    final static String defaultDataCode_GL_Preffix = "_GL";
    /**
     * 获取项目编码
     *
     * @param dataCode 项目下任意节点数字化编码
     * @return
     */
    public static String getProjectDataCode(String dataCode) {
        // 添加空值和长度检查
        if (dataCode == null || dataCode.length() < 8) {
            throw new IllegalArgumentException("dataCode不能为空且长度必须至少为8个字符，实际长度: " +
                (dataCode == null ? "null" : dataCode.length()) + ", dataCode: " + dataCode);
        }
        return defaultDataCode.replaceFirst(Regex_P, dataCode.substring(0, 8));
    }

    /**
     * 识别属性为基础属性、静态属性、动态属性
     *
     * @param insDataCode 实例数字化编码
     * @return
     */
    public static PropClassify getPropClassify(String insDataCode) {
//        dataCodeCheck(insDataCode);
        String t = null;
        if (insDataCode.indexOf(defaultDataCode_GL_Preffix) != -1) {
            t = insDataCode.substring(54, 55);
        } else {
            t = insDataCode.substring(59, 60);
        }
        if (PropClassify.DyProp.getCode().equals(t)) {
            return PropClassify.DyProp;
        } else if (PropClassify.StProp.getCode().equals(t)) {
            return PropClassify.StProp;
        } else if (PropClassify.Base.getCode().equals(t)) {
            return PropClassify.Base;
        }
        return null;
    }

}
