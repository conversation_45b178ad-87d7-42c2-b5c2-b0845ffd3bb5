# 数据库和日志优化配置
spring:
  # 数据源配置优化
  datasource:
    hikari:
      # 连接池配置优化
      minimum-idle: 5
      maximum-pool-size: 20
      idle-timeout: 300000
      max-lifetime: 1800000
      connection-timeout: 30000
      validation-timeout: 5000
      leak-detection-threshold: 60000
      # 禁用不必要的日志
      register-mbeans: false
      # 连接测试查询
      connection-test-query: SELECT 1
      # 连接初始化SQL
      connection-init-sql: SET NAMES utf8mb4
      
  # JPA/Hibernate 配置优化
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        format_sql: false
        use_sql_comments: false
        generate_statistics: false
        jdbc:
          batch_size: 50
          batch_versioned_data: true
        order_inserts: true
        order_updates: true
        
# MyBatis-Plus 配置优化
mybatis-plus:
  configuration:
    # 关闭日志实现
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
    # 开启缓存
    cache-enabled: true
    # 延迟加载
    lazy-loading-enabled: false
    aggressive-lazy-loading: false
    # 允许多结果集
    multiple-result-sets-enabled: true
    # 使用列标签
    use-column-label: true
    # 使用生成的键
    use-generated-keys: false
    # 自动映射行为
    auto-mapping-behavior: partial
    auto-mapping-unknown-column-behavior: warning
    # 默认执行器类型
    default-executor-type: reuse
    # 默认语句超时时间
    default-statement-timeout: 25
    # 默认获取数据大小
    default-fetch-size: 100
    # 安全的行边界
    safe-row-bounds-enabled: false
    # 本地缓存作用域
    local-cache-scope: session
    # 空值的JDBC类型
    jdbc-type-for-null: other
    # 当结果集中含有Null值时是否执行映射对象的setter或者Map对象的put方法
    call-setters-on-nulls: false
    # 当返回行的所有列都是空时，MyBatis默认返回null
    return-instance-for-empty-row: false
  global-config:
    # 关闭 banner
    banner: false
    db-config:
      # 逻辑删除配置
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      
# 日志配置优化
logging:
  level:
    # MyBatis 相关日志级别
    org.apache.ibatis: WARN
    com.baomidou.mybatisplus: WARN
    # 数据库连接池日志级别
    com.zaxxer.hikari: WARN
    # Spring 事务日志级别
    org.springframework.jdbc: WARN
    org.springframework.transaction: WARN
    # 数据库驱动日志级别
    com.mysql.cj: WARN
    # SQL 日志级别
    druid.sql: WARN
    # 根日志级别
    root: INFO
    # 应用日志级别
    com.siact.energy.cal: INFO
    
# 定时任务优化配置
task:
  # 线程池配置
  execution:
    pool:
      core-size: 5
      max-size: 10
      queue-capacity: 100
      keep-alive: 60s
      thread-name-prefix: "task-"
      
# 数据源检查任务配置
data-source:
  task:
    enable: true
    # 改为每小时执行一次，减少频率
    cron: "0 0 0/1 * * ?"
    
# 项目更新任务配置  
update-project:
  task:
    enable: true
    # 改为每10分钟执行一次，减少频率
    cron: "0 0/10 * * * ?"

# 性能监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      simple:
        enabled: false
